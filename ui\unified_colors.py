# -*- coding: utf-8 -*-
"""
ملف الألوان والأنماط الموحدة
Unified Colors and Styles
"""

class UnifiedColors:
    """كلاس موحد للألوان المرجعية لتجنب التكرار"""
    
    # الألوان الأساسية
    PRIMARY = '#3B82F6'          # أزرق أساسي
    SECONDARY = '#6B7280'        # رمادي ثانوي
    SUCCESS = '#10B981'          # أخضر للنجاح
    WARNING = '#F59E0B'          # برتقالي للتحذير
    ERROR = '#EF4444'            # أحمر للخطأ
    INFO = '#3B82F6'             # أزرق للمعلومات
    
    # ألوان الأرصدة
    POSITIVE_BALANCE = '#00FF7F'  # أخضر نيون للإيجابي
    NEGATIVE_BALANCE = '#FF6B6B'  # أحمر نيون للسلبي
    NEUTRAL_BALANCE = '#E2E8F0'   # رمادي فاتح للمحايد
    
    # ألوان الأولوية
    HIGH_PRIORITY = '#FF4500'     # برتقالي محمر للعالي
    MEDIUM_PRIORITY = '#FFD700'   # ذهبي للمتوسط
    LOW_PRIORITY = '#C0C0C0'      # فضي للمنخفض
    
    # ألوان خاصة
    VIP_COLOR = '#FFD700'         # ذهبي للمميزين
    NEW_COLOR = '#00FFFF'         # سماوي نيون للجدد
    NORMAL_COLOR = '#DA70D6'      # بنفسجي نيون للعاديين
    DEFAULT_COLOR = '#FFFFFF'     # أبيض نقي افتراضي
    
    # ألوان الخلفية
    BACKGROUND_LIGHT = '#F8FAFC'
    BACKGROUND_DARK = '#1E293B'
    BACKGROUND_CARD = '#FFFFFF'
    
    # ألوان النص
    TEXT_PRIMARY = '#1F2937'
    TEXT_SECONDARY = '#6B7280'
    TEXT_LIGHT = '#9CA3AF'
    TEXT_WHITE = '#FFFFFF'
    
    @classmethod
    def get_balance_color(cls, balance):
        """الحصول على لون الرصيد بناءً على القيمة"""
        if balance > 0:
            return cls.POSITIVE_BALANCE
        elif balance < 0:
            return cls.NEGATIVE_BALANCE
        else:
            return cls.NEUTRAL_BALANCE
    
    @classmethod
    def get_priority_color(cls, priority):
        """الحصول على لون الأولوية"""
        priority_colors = {
            'high': cls.HIGH_PRIORITY,
            'medium': cls.MEDIUM_PRIORITY,
            'low': cls.LOW_PRIORITY,
            'عالي': cls.HIGH_PRIORITY,
            'متوسط': cls.MEDIUM_PRIORITY,
            'منخفض': cls.LOW_PRIORITY
        }
        return priority_colors.get(priority.lower() if priority else '', cls.MEDIUM_PRIORITY)
    
    @classmethod
    def get_status_color(cls, status):
        """الحصول على لون الحالة"""
        status_colors = {
            'active': cls.SUCCESS,
            'inactive': cls.ERROR,
            'pending': cls.WARNING,
            'completed': cls.SUCCESS,
            'cancelled': cls.ERROR,
            'نشط': cls.SUCCESS,
            'غير نشط': cls.ERROR,
            'معلق': cls.WARNING,
            'مكتمل': cls.SUCCESS,
            'ملغي': cls.ERROR
        }
        return status_colors.get(status.lower() if status else '', cls.INFO)


class UnifiedStyles:
    """كلاس موحد للأنماط والتصاميم"""
    
    @staticmethod
    def get_button_style(color=UnifiedColors.PRIMARY):
        """نمط الأزرار الموحد"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 12px;
            }}
            QPushButton:hover {{
                background-color: {color}dd;
                transform: translateY(-1px);
            }}
            QPushButton:pressed {{
                background-color: {color}bb;
                transform: translateY(1px);
            }}
            QPushButton:disabled {{
                background-color: {UnifiedColors.TEXT_LIGHT};
                color: {UnifiedColors.TEXT_SECONDARY};
            }}
        """
    
    @staticmethod
    def get_input_style():
        """نمط حقول الإدخال الموحد"""
        return f"""
            QLineEdit, QTextEdit, QDoubleSpinBox, QSpinBox {{
                border: 2px solid {UnifiedColors.TEXT_LIGHT};
                border-radius: 6px;
                padding: 8px;
                font-size: 12px;
                background-color: {UnifiedColors.BACKGROUND_CARD};
            }}
            QLineEdit:focus, QTextEdit:focus, QDoubleSpinBox:focus, QSpinBox:focus {{
                border-color: {UnifiedColors.PRIMARY};
                outline: none;
            }}
        """
    
    @staticmethod
    def get_table_style():
        """نمط الجداول الموحد"""
        return f"""
            QTableWidget {{
                gridline-color: {UnifiedColors.TEXT_LIGHT};
                background-color: {UnifiedColors.BACKGROUND_CARD};
                alternate-background-color: {UnifiedColors.BACKGROUND_LIGHT};
                selection-background-color: {UnifiedColors.PRIMARY}33;
                border: 1px solid {UnifiedColors.TEXT_LIGHT};
                border-radius: 6px;
            }}
            QTableWidget::item {{
                padding: 8px;
                border: none;
            }}
            QTableWidget::item:selected {{
                background-color: {UnifiedColors.PRIMARY}44;
                color: {UnifiedColors.TEXT_PRIMARY};
            }}
            QHeaderView::section {{
                background-color: {UnifiedColors.BACKGROUND_LIGHT};
                color: {UnifiedColors.TEXT_PRIMARY};
                padding: 8px;
                border: none;
                font-weight: bold;
            }}
        """
    
    @staticmethod
    def get_card_style():
        """نمط البطاقات الموحد"""
        return f"""
            QFrame {{
                background-color: {UnifiedColors.BACKGROUND_CARD};
                border: 1px solid {UnifiedColors.TEXT_LIGHT};
                border-radius: 8px;
                padding: 16px;
            }}
        """
    
    @staticmethod
    def get_label_style(color=UnifiedColors.TEXT_PRIMARY):
        """نمط التسميات الموحد"""
        return f"""
            QLabel {{
                color: {color};
                font-size: 12px;
                font-weight: normal;
            }}
        """


class UnifiedConstants:
    """ثوابت موحدة للنظام"""
    
    # أحجام الخط
    FONT_SIZE_SMALL = 10
    FONT_SIZE_NORMAL = 12
    FONT_SIZE_LARGE = 14
    FONT_SIZE_XLARGE = 16
    
    # المسافات
    SPACING_SMALL = 4
    SPACING_NORMAL = 8
    SPACING_LARGE = 16
    SPACING_XLARGE = 24
    
    # أحجام الأيقونات
    ICON_SIZE_SMALL = 16
    ICON_SIZE_NORMAL = 24
    ICON_SIZE_LARGE = 32
    
    # أحجام النوافذ
    DIALOG_MIN_WIDTH = 400
    DIALOG_MIN_HEIGHT = 300
    WINDOW_MIN_WIDTH = 800
    WINDOW_MIN_HEIGHT = 600
    
    # العملة الافتراضية
    DEFAULT_CURRENCY = 'ج.م'
    
    # تنسيقات التاريخ
    DATE_FORMAT = '%Y-%m-%d'
    DATETIME_FORMAT = '%Y-%m-%d %H:%M:%S'
    DISPLAY_DATE_FORMAT = '%d/%m/%Y'
    DISPLAY_DATETIME_FORMAT = '%d/%m/%Y %H:%M'


class UnifiedValidators:
    """مُتحققات موحدة للبيانات"""
    
    @staticmethod
    def validate_email(email):
        """التحقق من صحة البريد الإلكتروني"""
        import re
        if not email:
            return True  # البريد الإلكتروني اختياري
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    @staticmethod
    def validate_phone(phone):
        """التحقق من صحة رقم الهاتف"""
        import re
        if not phone:
            return True  # رقم الهاتف اختياري
        pattern = r'^[\d\s\-\+\(\)]{7,15}$'
        return re.match(pattern, phone) is not None
    
    @staticmethod
    def validate_amount(amount):
        """التحقق من صحة المبلغ"""
        try:
            float_amount = float(amount)
            return float_amount >= 0
        except (ValueError, TypeError):
            return False
    
    @staticmethod
    def validate_required_field(value):
        """التحقق من الحقول المطلوبة"""
        return value is not None and str(value).strip() != ''


class UnifiedHelpers:
    """مساعدات موحدة للنظام"""
    
    @staticmethod
    def format_currency(amount, currency=UnifiedConstants.DEFAULT_CURRENCY):
        """تنسيق العملة"""
        try:
            return f"{float(amount):,.2f} {currency}"
        except (ValueError, TypeError):
            return f"0.00 {currency}"
    
    @staticmethod
    def format_date(date_obj, format_str=UnifiedConstants.DISPLAY_DATE_FORMAT):
        """تنسيق التاريخ"""
        try:
            if hasattr(date_obj, 'strftime'):
                return date_obj.strftime(format_str)
            return str(date_obj)
        except:
            return ""
    
    @staticmethod
    def safe_float(value, default=0.0):
        """تحويل آمن إلى رقم عشري"""
        try:
            return float(value)
        except (ValueError, TypeError):
            return default
    
    @staticmethod
    def safe_int(value, default=0):
        """تحويل آمن إلى رقم صحيح"""
        try:
            return int(value)
        except (ValueError, TypeError):
            return default
