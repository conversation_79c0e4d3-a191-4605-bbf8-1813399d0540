# -*- coding: utf-8 -*-
"""
مدير الأرصدة
Balance Manager Service
"""

from database import Client, Supplier, Installment, get_session
from utils import auto_create_installment_for_negative_balance


class BalanceManager:
    """مدير الأرصدة - فصل منطق إدارة الأرصدة عن طبقة قاعدة البيانات"""
    
    def __init__(self, session=None):
        """تهيئة مدير الأرصدة"""
        self.session = session or get_session()
    
    def update_client_balance(self, client_id, amount, operation='add', auto_create_installment=True):
        """تحديث رصيد العميل مع إدارة الأقساط التلقائية"""
        try:
            # استيراد دالة التحديث من قاعدة البيانات
            from database import update_client_balance as db_update_client_balance
            
            # الحصول على الرصيد القديم
            client = self.session.query(Client).filter_by(id=client_id).first()
            if not client:
                raise ValueError(f"العميل غير موجود: {client_id}")
            
            old_balance = client.balance or 0
            
            # تحديث الرصيد في قاعدة البيانات
            result = db_update_client_balance(
                self.session, client_id, amount, operation, auto_create_installment=False
            )
            
            # التحقق من الرصيد الجديد وإنشاء قسط إذا لزم الأمر
            if auto_create_installment and result:
                new_balance = client.balance or 0
                if old_balance >= 0 and new_balance < 0:
                    self._create_installment_for_negative_balance(
                        'client', client_id, client.name, abs(new_balance)
                    )
            
            return result
            
        except Exception as e:
            print(f"❌ خطأ في تحديث رصيد العميل: {str(e)}")
            return False
    
    def update_supplier_balance(self, supplier_id, amount, operation='add', auto_create_installment=True):
        """تحديث رصيد المورد مع إدارة الأقساط التلقائية"""
        try:
            # استيراد دالة التحديث من قاعدة البيانات
            from database import update_supplier_balance as db_update_supplier_balance
            
            # الحصول على الرصيد القديم
            supplier = self.session.query(Supplier).filter_by(id=supplier_id).first()
            if not supplier:
                raise ValueError(f"المورد غير موجود: {supplier_id}")
            
            old_balance = supplier.balance or 0
            
            # تحديث الرصيد في قاعدة البيانات
            result = db_update_supplier_balance(
                self.session, supplier_id, amount, operation, auto_create_installment=False
            )
            
            # التحقق من الرصيد الجديد وإنشاء قسط إذا لزم الأمر
            if auto_create_installment and result:
                new_balance = supplier.balance or 0
                if old_balance >= 0 and new_balance < 0:
                    self._create_installment_for_negative_balance(
                        'supplier', supplier_id, supplier.name, abs(new_balance)
                    )
            
            return result
            
        except Exception as e:
            print(f"❌ خطأ في تحديث رصيد المورد: {str(e)}")
            return False
    
    def _create_installment_for_negative_balance(self, entity_type, entity_id, entity_name, amount):
        """إنشاء قسط للرصيد السالب"""
        try:
            auto_create_installment_for_negative_balance(
                self.session, entity_type, entity_id, entity_name, amount
            )
            print(f"🔄 تم إنشاء قسط تلقائي للـ{entity_type} {entity_name} بسبب الرصيد السالب: {amount:,.2f} ج.م")
        except Exception as e:
            print(f"⚠️ خطأ في إنشاء القسط التلقائي للـ{entity_type} {entity_name}: {str(e)}")
    
    def check_negative_balances(self):
        """فحص الأرصدة السالبة وإنشاء أقساط تلقائية"""
        try:
            print("🔍 بدء الفحص التلقائي للأرصدة السالبة...")
            
            # فحص العملاء
            clients_with_negative = self.session.query(Client).filter(Client.balance < 0).all()
            suppliers_with_negative = self.session.query(Supplier).filter(Supplier.balance < 0).all()
            
            installments_created = 0
            
            # إنشاء أقساط للعملاء ذوي الأرصدة السالبة
            for client in clients_with_negative:
                try:
                    # التحقق من عدم وجود قسط مفتوح بالفعل
                    existing_installment = self.session.query(Installment).filter_by(
                        client_id=client.id,
                        status='مفتوح'
                    ).first()
                    
                    if not existing_installment:
                        self._create_installment_for_negative_balance(
                            'client', client.id, client.name, abs(client.balance)
                        )
                        installments_created += 1
                except Exception as e:
                    print(f"⚠️ خطأ في معالجة العميل {client.name}: {str(e)}")
            
            # إنشاء أقساط للموردين ذوي الأرصدة السالبة
            for supplier in suppliers_with_negative:
                try:
                    # التحقق من عدم وجود قسط مفتوح بالفعل
                    existing_installment = self.session.query(Installment).filter_by(
                        supplier_id=supplier.id,
                        status='مفتوح'
                    ).first()
                    
                    if not existing_installment:
                        self._create_installment_for_negative_balance(
                            'supplier', supplier.id, supplier.name, abs(supplier.balance)
                        )
                        installments_created += 1
                except Exception as e:
                    print(f"⚠️ خطأ في معالجة المورد {supplier.name}: {str(e)}")
            
            stats = {
                'clients_checked': self.session.query(Client).count(),
                'suppliers_checked': self.session.query(Supplier).count(),
                'clients_with_negative_balance': len(clients_with_negative),
                'suppliers_with_negative_balance': len(suppliers_with_negative),
                'installments_created': installments_created,
                'errors': 0
            }

            print(f"📊 نتائج الفحص التلقائي:")
            print(f"   - العملاء المفحوصين: {stats['clients_checked']}")
            print(f"   - الموردين المفحوصين: {stats['suppliers_checked']}")
            print(f"   - العملاء بأرصدة سالبة: {stats['clients_with_negative_balance']}")
            print(f"   - الموردين بأرصدة سالبة: {stats['suppliers_with_negative_balance']}")
            print(f"   - الأقساط المُنشأة: {stats['installments_created']}")

            return stats

        except Exception as e:
            print(f"❌ خطأ في الفحص التلقائي للأرصدة السالبة: {str(e)}")
            return {
                'clients_checked': 0,
                'suppliers_checked': 0,
                'clients_with_negative_balance': 0,
                'suppliers_with_negative_balance': 0,
                'installments_created': 0,
                'errors': 1
            }
    
    def get_balance_summary(self):
        """الحصول على ملخص الأرصدة"""
        try:
            # إحصائيات العملاء
            total_clients = self.session.query(Client).count()
            clients_positive = self.session.query(Client).filter(Client.balance > 0).count()
            clients_negative = self.session.query(Client).filter(Client.balance < 0).count()
            clients_zero = self.session.query(Client).filter(Client.balance == 0).count()
            
            # إحصائيات الموردين
            total_suppliers = self.session.query(Supplier).count()
            suppliers_positive = self.session.query(Supplier).filter(Supplier.balance > 0).count()
            suppliers_negative = self.session.query(Supplier).filter(Supplier.balance < 0).count()
            suppliers_zero = self.session.query(Supplier).filter(Supplier.balance == 0).count()
            
            # مجموع الأرصدة
            total_client_balance = sum([c.balance or 0 for c in self.session.query(Client).all()])
            total_supplier_balance = sum([s.balance or 0 for s in self.session.query(Supplier).all()])
            
            return {
                'clients': {
                    'total': total_clients,
                    'positive': clients_positive,
                    'negative': clients_negative,
                    'zero': clients_zero,
                    'total_balance': total_client_balance
                },
                'suppliers': {
                    'total': total_suppliers,
                    'positive': suppliers_positive,
                    'negative': suppliers_negative,
                    'zero': suppliers_zero,
                    'total_balance': total_supplier_balance
                }
            }
            
        except Exception as e:
            print(f"❌ خطأ في الحصول على ملخص الأرصدة: {str(e)}")
            return None


# مثيل عام لمدير الأرصدة
balance_manager = BalanceManager()


def get_balance_manager(session=None):
    """الحصول على مثيل مدير الأرصدة"""
    if session:
        return BalanceManager(session)
    return balance_manager
